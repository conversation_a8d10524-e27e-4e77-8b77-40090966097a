import { logger } from '@utils/logger';
import { Platform } from 'react-native';
import {
    getSoundFileName,
    NOTIFICATION_SOUND_CONFIG,
} from '@config/notificationSound';

export interface NotificationSoundConfig {
    soundName: string;
    enabled: boolean;
}

export class NotificationSoundService {

    private static currentSoundConfig: NotificationSoundConfig = {
        soundName: getSoundFileName(),
        enabled: NOTIFICATION_SOUND_CONFIG.DEFAULTS.ENABLED,
    };

    /**
     * Get the sound name to use for notifications
     * Returns null if custom sounds are disabled (use system default)
     */
    public static getSoundNameForNotification(): string | null {
        if (!this.currentSoundConfig.enabled) {
            if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
                logger.info('Custom notification sound is disabled, using system default');
            }
            return null; // Use system default
        }

        if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
            logger.info('Custom notification sound enabled:', {
                soundName: this.currentSoundConfig.soundName,
                platform: Platform.OS,
                enabled: this.currentSoundConfig.enabled
            });
        }

        return this.currentSoundConfig.soundName;
    }
}
