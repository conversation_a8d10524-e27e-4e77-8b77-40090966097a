{"name": "bp-pulse-app", "version": "4.1.0", "private": true, "description": "bp pulse mobile application shell built with React Native", "license": "ISC", "scripts": {"adb:reverse:debug": "adb reverse tcp:8081 tcp:8081", "adb:reverse:server": "adb reverse tcp:4000 tcp:4000", "adb:reverse:all": "sh scripts/adbReverseAllDevices.sh", "android": "react-native run-android --mode=localDebug --appIdSuffix=debug", "android:release": "yarn bundle && react-native run-android --mode=localRelease --appIdSuffix=debug", "bundle": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle", "clean": "(cd android && ./gradlew clean) && (cd ios && xcodebuild clean)", "changelog": "generate-changelog -u https://dev.azure.com/bp-digital/DST-Digital_AMU/_git/bp-pulse-mobile-app", "extract-deps": "node scripts/extract-deps/extract-deps.ts", "generate-app-env": "sh scripts/generate-env-vars/generate-app-env.sh > .env", "generate-gradle-properties": "sh scripts/generate-config/generate-gradle-properties.sh > android/gradle.properties", "ios": "yarn ios:pods && react-native run-ios --mode Debug", "ios:pods": "(cd ios && pod install)", "ios:pods:update": "(cd ios && pod install --repo-update)", "ios:release": "react-native run-ios --mode Release", "ios:small": "react-native run-ios --simulator 'iPhone SE (2nd generation)'", "ios:ipad": "react-native run-ios --simulator 'iPad (10th generation)'", "link": "yarn react-native link", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx .", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "maestro:install": "curl -Ls \"https://get.maestro.mobile.dev\" | bash", "maestro:studio": "maestro studio", "maestro:test:android": "(cd maestro && maestro test . @.env -e APP_ID=com.aml.evapp.debug)", "maestro:test:android:parallel": "sh scripts/maestro/runMaestroParallel.sh android Phone 35 com.aml.evapp.debug", "maestro:test:ios": "(cd maestro && maestro test . @.env -e APP_ID=com.aml.ev-app.debug)", "maestro:test:ios:parallel": "sh scripts/maestro/runMaestroParallel.sh ios iPhone15 17 com.aml.ev-app.debug", "maestro:devices:launch:ios": "sh scripts/maestro/launchMaestroDevices.sh ios", "maestro:devices:launch:android": "sh scripts/maestro/launchMaestroDevices.sh android", "maestro:generate-translations": "npx ts-node scripts/maestro/convertTranslationsToJs.ts", "pre-commit": "yarn prettier:write", "pre-push": "yarn types && yarn lint && yarn test:ci", "prereqs": "yarn react-native doctor", "prettier:write": "yarn prettier --write '**/*.{js,ts,tsx,json}' && yarn prettier --write '*.{js,json}'", "reset": "watchman watch-del-all && react-native start --reset-cache", "start": "react-native start", "test": "jest --watchAll --coverage -c jest.config.js", "test:ci": "jest --ci --coverage --passWithNoTests --verbose -c jest.config.js", "test:update": "jest --updateSnapshot -c jest.config.js", "types": "tsc --noEmit", "update:browserlist": "yarn browserslist@latest --update-db"}, "dependencies": {"@bp/bppay-wallet-feature": "6.31.1", "@bp/charge-history-mfe": "2.0.48", "@bp/charge-mfe": "^3.53.0", "@bp/credit-mfe": "workspace:*", "@bp/favourites-mfe": "2.1.0", "@bp/guest_feature-mfe": "0.1.12", "@bp/map-mfe": "^1.8.4", "@bp/mfe-helper-apollo": "0.0.3", "@bp/mfe-subscription": "2.159.2", "@bp/offers-mfe": "workspace:*", "@bp/onboarding-mfe": "0.2.8", "@bp/partnerdriver-mfe": "3.12.0", "@bp/profile-mfe": "1.67.0", "@bp/pulse-auth-sdk": "0.3.1", "@bp/pulse-mobile-sdk": "patch:@bp/pulse-mobile-sdk@patch%3A@bp/pulse-mobile-sdk@npm%253A3.11.0%23~/.yarn/patches/@bp-pulse-mobile-sdk-npm-3.9.0-0a9ae682e7.patch%3A%3Aversion=3.11.0&hash=d09af1#~/.yarn/patches/@bp-pulse-mobile-sdk-patch-46bef3765e.patch", "@bp/pulse-shared-types": "1.3.0", "@bp/registration-mfe": "workspace:*", "@bp/rfid-mfe": "1.67.1", "@bp/rtbf-mfe": "2.15.2", "@bp/ui-components": "12.15.1", "@react-native-async-storage/async-storage": "^1.17.9", "@react-native-community/geolocation": "^3.4.0", "@react-native-firebase/analytics": "19.2.0", "@react-native-firebase/app": "19.2.0", "@react-native-firebase/crashlytics": "19.2.0", "@react-native-firebase/remote-config": "19.2.0", "@react-navigation/bottom-tabs": "6.5.11", "@react-navigation/native": "6.1.9", "@react-navigation/stack": "6.3.20", "@ua/react-native-airship": "21.3.0", "date-fns": "2.30.0", "i18next": "^23.6.0", "intl-pluralrules": "1.3.0", "lodash.defaultsdeep": "^4.6.1", "lottie-react-native": "6.4.1", "moment": "2.29.4", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-i18next": "^13.3.0", "react-native": "0.72.7", "react-native-bootsplash": "5.1.3", "react-native-config": "1.4.6", "react-native-device-info": "^9.0.2", "react-native-email-link": "^1.14.0", "react-native-encrypted-storage": "4.0.3", "react-native-gesture-handler": "2.14.0", "react-native-get-random-values": "1.10.0", "react-native-inappbrowser-reborn": "3.7.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-maps": "1.8.0", "react-native-reanimated-carousel": "3.5.1", "react-native-screens": "3.15.0", "react-native-svg": "14.1.0", "react-native-svg-transformer": "1.1.0", "react-native-tracking-transparency": "^0.1.1", "react-native-webview": "^13.6.3", "semver": "^7.5.3", "styled-components": "6.1.13"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/runtime": "^7.23.3", "@bp/eslint-plugin": "^0.0.3", "@calm/eslint-plugin-react-intl": "^1.4.1", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@formatjs/intl-locale": "^3.4.3", "@formatjs/intl-numberformat": "^8.9.0", "@react-native-community/eslint-config": "^3.1.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@testing-library/react-native": "^12.3.0", "@tsconfig/react-native": "^3.0.0", "@types/cli-color": "^2.0.2", "@types/intl": "^1", "@types/jest": "^29.5.14", "@types/lodash.defaultsdeep": "^4.6.9", "@types/react": "^18.0.24", "@types/react-native-get-random-values": "^1", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "babel-jest": "^29.7.0", "babel-plugin-date-fns": "^2.0.0", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.55.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-ft-flow": "^3.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-native": "^4.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-sonarjs": "^0.21.0", "eslint-plugin-typescript-sort-keys": "^3.1.0", "eslint-plugin-you-dont-need-lodash-underscore": "^6.13.0", "generate-changelog": "^1.8.0", "jest": "29.7.0", "jest-environment-node": "29.7.0", "jest-junit": "^14.0.0", "metro-react-native-babel-preset": "0.76.8", "obfuscator-io-metro-plugin": "^2.1.3", "prettier": "^2.7.1", "react-native-circular-progress": "1.3.9", "react-native-reanimated": "3.11.0", "react-native-safe-area-context": "4.7.4", "react-native-url-polyfill": "1.3.0", "react-test-renderer": "18.2.0", "typescript": "~5.1.6"}, "peerDependencies": {"@apollo/client": "3.8.7", "@apollo/react-hooks": "4.0.0", "@bp/mfe-helper-apollo": "^0.0.3", "@bp/react-native-cardinal-sdk": "0.0.78", "@react-native-community/masked-view": "0.1.11", "@react-native-community/netinfo": "9.3.0", "cli-color": "2.0.3", "graphql": "16.6.0", "intl": "1.2.5", "react": "18.2.0", "react-i18next": "13.2.1", "react-native-appsflyer": "6.12.2", "react-native-file-viewer": "2.1.5", "react-native-fs": "2.20.0", "react-native-geolocation-service": "5.3.1", "react-native-map-link": "2.10.2", "react-native-segmented-control-tab": "4.0.0", "react-native-share": "7.8.0", "react-native-url-polyfill": "1.3.0", "rn-fetch-blob": "0.12.0"}, "engines": {"node": "22.12.0", "npm": "10.9.0"}}